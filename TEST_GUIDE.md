# 🧪 测试指南：活动栏React插件

## 📋 测试步骤

### 1. 构建项目
```bash
pnpm run compile
```

### 2. 启动扩展开发
1. 在VS Code中打开项目
2. 按 `F5` 启动扩展开发主机
3. 等待新的VS Code窗口打开

### 3. 检查活动栏
在新窗口中，您应该看到：
- **左侧活动栏**：有一个新的图标（圆形带勾选标记）
- 图标标题显示为 "AI Code"

### 4. 测试React面板
1. **点击活动栏图标**
   - 应该打开侧边栏
   - 显示 "React Panel" 标题
   - 包含React应用界面

2. **测试React功能**
   - 点击"增加"按钮 → 计数器应该增加
   - 点击"重置"按钮 → 计数器应该归零
   - 点击"发送消息到扩展" → 应该显示VS Code通知

### 5. 测试命令面板
1. 按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac)
2. 搜索 "打开React页面"
3. 执行命令 → 应该打开独立的React页面

### 6. 测试资源管理器面板
1. 点击资源管理器图标（文件夹图标）
2. 向下滚动查找 "AI Code React" 面板
3. 应该显示相同的React应用

## 🔍 故障排除

### 活动栏没有图标？
1. 确保构建成功：`pnpm run compile`
2. 检查控制台是否有错误
3. 重新加载扩展窗口：`Ctrl+R`

### React面板空白？
1. 检查 `dist/webview.js` 文件是否存在
2. 查看开发者工具控制台错误
3. 确保HTML模板路径正确

### 通信不工作？
1. 检查webview的消息处理函数
2. 确保vscode API正确声明
3. 查看扩展主机的输出面板

## 📊 预期结果

✅ **活动栏**：显示AI Code图标  
✅ **React面板**：功能完整的React应用  
✅ **双向通信**：按钮点击触发VS Code通知  
✅ **状态持久化**：关闭重开后状态保持  
✅ **主题适配**：自动适应VS Code主题  

## 🎯 成功标志

如果您看到以下内容，说明集成成功：
1. 活动栏有新图标
2. 点击图标显示React界面
3. 计数器功能正常
4. 消息通信正常
5. 样式与VS Code主题一致

## 🚀 下一步

成功测试后，您可以：
1. 修改 `src/webview/App.tsx` 添加自定义功能
2. 在 `src/extension.ts` 中添加更多扩展API调用
3. 扩展React组件库
4. 添加更多命令和菜单项

祝您开发愉快！🎉
