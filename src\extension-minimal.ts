import * as vscode from 'vscode';

// 最简单的webview provider
class MinimalWebviewProvider implements vscode.WebviewViewProvider {
    constructor(private readonly extensionUri: vscode.Uri) {}

    resolveWebviewView(webviewView: vscode.WebviewView): void {
        console.log('MinimalWebviewProvider: resolveWebviewView called');
        
        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this.extensionUri]
        };

        webviewView.webview.html = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Minimal Test</title>
                <style>
                    body { 
                        font-family: Arial, sans-serif; 
                        padding: 20px; 
                        background: #1e1e1e; 
                        color: #cccccc; 
                    }
                    button { 
                        background: #0e639c; 
                        color: white; 
                        border: none; 
                        padding: 10px 20px; 
                        cursor: pointer; 
                        margin: 10px 0;
                        display: block;
                    }
                    button:hover { background: #1177bb; }
                </style>
            </head>
            <body>
                <h1>🎉 Webview 工作了！</h1>
                <p>如果您能看到这个页面，说明webview provider已经正确注册和工作。</p>
                <button onclick="testAlert()">测试消息</button>
                <p id="status">状态：已加载</p>
                
                <script>
                    const vscode = acquireVsCodeApi();
                    
                    function testAlert() {
                        vscode.postMessage({ 
                            command: 'showMessage', 
                            text: '来自webview的消息！' 
                        });
                        document.getElementById('status').textContent = '状态：消息已发送';
                    }
                    
                    console.log('Minimal webview loaded successfully');
                </script>
            </body>
            </html>
        `;

        // 监听消息
        webviewView.webview.onDidReceiveMessage(message => {
            if (message.command === 'showMessage') {
                vscode.window.showInformationMessage(message.text);
            }
        });
    }
}

export function activate(context: vscode.ExtensionContext) {
    console.log('=== MINIMAL EXTENSION ACTIVATING ===');
    
    try {
        // 注册最简单的webview provider
        const provider = new MinimalWebviewProvider(context.extensionUri);
        
        console.log('Registering webview provider for aicode.reactView...');
        const disposable = vscode.window.registerWebviewViewProvider(
            'aicode.reactView', 
            provider
        );
        
        context.subscriptions.push(disposable);
        console.log('Webview provider registered successfully');
        
        // 注册一个简单命令来测试
        const commandDisposable = vscode.commands.registerCommand('aicode.test', () => {
            vscode.window.showInformationMessage('测试命令工作正常！');
        });
        
        context.subscriptions.push(commandDisposable);
        console.log('Test command registered');
        
        console.log('=== MINIMAL EXTENSION ACTIVATED SUCCESSFULLY ===');
        
    } catch (error) {
        console.error('Error during activation:', error);
        vscode.window.showErrorMessage(`扩展激活失败: ${error}`);
    }
}

export function deactivate() {
    console.log('=== MINIMAL EXTENSION DEACTIVATED ===');
}
