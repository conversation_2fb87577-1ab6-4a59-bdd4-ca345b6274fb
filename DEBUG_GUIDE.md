# 🔍 调试指南：活动栏Webview问题

## 🚨 问题现象
活动栏中有图标，但点击后没有显示webview页面内容。

## 🛠️ 已修复的问题

### 1. ✅ 激活事件
添加了正确的激活事件：
```json
"activationEvents": [
  "onView:aicode.reactView",
  "onView:aicode.explorerView",
  "onCommand:aicode.openReactPage",
  "onCommand:aicode.helloWorld"
]
```

### 2. ✅ 图标路径
修复了图标路径，创建了根目录的 `icon.svg` 文件。

### 3. ✅ 内容安全策略
更新了CSP以支持更多资源类型：
```html
<meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src 'unsafe-inline' vscode-resource: https:; script-src 'unsafe-inline' vscode-resource: https:; img-src vscode-resource: https: data:;">
```

### 4. ✅ 调试日志
添加了详细的调试日志来跟踪问题。

## 🔍 调试步骤

### 1. 检查扩展是否激活
1. 按 `F5` 启动扩展开发主机
2. 在新窗口中按 `Ctrl+Shift+P`
3. 运行 "Developer: Show Running Extensions"
4. 查找 "aicode" 扩展是否在列表中

### 2. 查看控制台日志
1. 在扩展开发主机中按 `Ctrl+Shift+P`
2. 运行 "Developer: Toggle Developer Tools"
3. 查看控制台是否有以下日志：
   - "Congratulations, your extension "aicode" is now active!"
   - "Registering aicode.reactView provider..."
   - "Resolving webview view..."

### 3. 检查活动栏
1. 查看左侧活动栏是否有AI Code图标
2. 点击图标，侧边栏应该打开
3. 如果侧边栏是空白的，检查开发者工具的控制台

### 4. 测试命令面板
1. 按 `Ctrl+Shift+P`
2. 搜索 "打开React页面"
3. 执行命令，应该打开独立的webview面板

## 🎯 预期行为

### 正常情况下应该看到：
1. **活动栏图标**：圆形带勾选标记的图标
2. **点击图标**：侧边栏打开，显示 "React Panel" 标题
3. **面板内容**：
   - "AI Code Extension" 标题
   - "Hello from React!" 消息
   - 计数器和按钮
   - 功能说明列表

### 如果看到空白面板：
1. 检查开发者工具控制台的错误
2. 确认 `dist/webview.js` 文件存在
3. 检查网络请求是否成功加载脚本

## 🔧 故障排除

### 问题1：活动栏没有图标
**解决方案**：
- 确保 `icon.svg` 文件在根目录
- 重新加载扩展窗口 (`Ctrl+R`)

### 问题2：有图标但点击无反应
**解决方案**：
- 检查扩展是否激活
- 查看控制台日志
- 确认webview provider注册成功

### 问题3：面板空白
**解决方案**：
- 检查 `dist/webview.js` 是否存在
- 查看控制台的JavaScript错误
- 确认CSP设置正确

### 问题4：React应用不加载
**解决方案**：
- 运行 `pnpm run compile` 重新构建
- 检查React组件是否有语法错误
- 确认esbuild配置正确

## 📝 手动测试清单

- [ ] 扩展激活成功
- [ ] 活动栏显示图标
- [ ] 点击图标打开侧边栏
- [ ] 侧边栏显示内容（至少有标题）
- [ ] React应用正常加载
- [ ] 按钮功能正常
- [ ] 消息通信正常

## 🚀 下一步

如果所有测试都通过，您可以：
1. 自定义React组件
2. 添加更多VS Code API集成
3. 扩展功能和样式

如果仍有问题，请检查：
1. VS Code版本兼容性
2. 扩展依赖是否正确安装
3. 项目文件结构是否完整
