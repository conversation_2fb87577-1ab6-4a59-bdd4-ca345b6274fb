{"name": "aicode", "displayName": "aicode", "description": "", "version": "0.0.1", "engines": {"vscode": "^1.102.0"}, "categories": ["Other"], "activationEvents": [], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "aicode.helloWorld", "title": "Hello World"}]}, "scripts": {"vscode:prepublish": "pnpm run package", "compile": "pnpm run check-types && pnpm run lint && node esbuild.js", "watch": "npm-run-all -p watch:*", "watch:esbuild": "node esbuild.js --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "package": "pnpm run check-types && pnpm run lint && node esbuild.js --production", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "pnpm run compile-tests && pnpm run compile && pnpm run lint", "check-types": "tsc --noEmit", "lint": "eslint src", "test": "vitest"}, "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "20.x", "@types/vscode": "^1.102.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "@vscode/test-cli": "^0.0.11", "@vscode/test-electron": "^2.5.2", "esbuild": "^0.25.3", "eslint": "^9.25.1", "npm-run-all": "^4.1.5", "typescript": "^5.8.3"}, "dependencies": {"vitest": "^3.2.4"}}