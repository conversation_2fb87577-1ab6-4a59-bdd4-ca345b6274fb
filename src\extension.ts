// The module 'vscode' contains the VS Code extensibility API
// Import the module and reference it with the alias vscode in your code below
import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';

/**
 * React Webview Provider类
 */
class ReactWebviewProvider implements vscode.WebviewViewProvider {
	public static readonly viewType = 'aicode.reactView';

	private _view?: vscode.WebviewView;

	constructor(private readonly _extensionUri: vscode.Uri) {}

	public resolveWebviewView(
		webviewView: vscode.WebviewView,
		_context: vscode.WebviewViewResolveContext,
		_token: vscode.CancellationToken,
	) {
		console.log('Resolving webview view...');
		this._view = webviewView;

		webviewView.webview.options = {
			enableScripts: true,
			localResourceRoots: [
				this._extensionUri
			]
		};

		const html = this._getHtmlForWebview(webviewView.webview);
		console.log('Generated HTML:', html.substring(0, 200) + '...');
		webviewView.webview.html = html;

		// 监听来自webview的消息
		webviewView.webview.onDidReceiveMessage(
			message => {
				switch (message.command) {
					case 'alert':
						vscode.window.showInformationMessage(message.data);
						return;
				}
			},
			undefined,
			[]
		);
	}

	private _getHtmlForWebview(webview: vscode.Webview) {
		// 获取webview脚本的URI
		const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'dist', 'webview.js'));
		console.log('Script URI:', scriptUri.toString());

		// 检查文件是否存在
		const scriptPath = path.join(this._extensionUri.fsPath, 'dist', 'webview.js');
		const scriptExists = fs.existsSync(scriptPath);
		console.log('Script file exists:', scriptExists, 'at path:', scriptPath);

		// 读取HTML模板
		const htmlPath = path.join(this._extensionUri.fsPath, 'src', 'webview', 'index.html');
		let html;

		try {
			html = fs.readFileSync(htmlPath, 'utf8');
			console.log('HTML template loaded successfully');
		} catch (error) {
			console.error('Failed to read HTML template:', error);
			// 提供一个简单的fallback HTML
			html = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Code Extension</title>
</head>
<body>
    <div id="root">
        <h1>AI Code Extension</h1>
        <p>React应用加载中...</p>
        <button onclick="testMessage()">测试消息</button>
    </div>
    <script>
        const vscode = acquireVsCodeApi();
        function testMessage() {
            vscode.postMessage({ command: 'alert', data: 'Hello from webview!' });
        }
    </script>
    <script src="{{webviewScript}}"></script>
</body>
</html>`;
		}

		// 替换脚本路径
		html = html.replace('{{webviewScript}}', scriptUri.toString());

		return html;
	}

	public sendMessage(command: string, data?: any) {
		if (this._view) {
			this._view.webview.postMessage({ command, data });
		}
	}
}

// This method is called when your extension is activated
// Your extension is activated the very first time the command is executed
export function activate(context: vscode.ExtensionContext) {

	console.log('Congratulations, your extension "aicode" is now active!');

	// 注册React webview provider for activity bar
	const reactProvider = new ReactWebviewProvider(context.extensionUri);
	console.log('Registering aicode.reactView provider...');
	context.subscriptions.push(
		vscode.window.registerWebviewViewProvider('aicode.reactView', reactProvider)
	);

	// 注册React webview provider for explorer
	const explorerProvider = new ReactWebviewProvider(context.extensionUri);
	console.log('Registering aicode.explorerView provider...');
	context.subscriptions.push(
		vscode.window.registerWebviewViewProvider('aicode.explorerView', explorerProvider)
	);

	// 原有的Hello World命令
	const disposable = vscode.commands.registerCommand('aicode.helloWorld', () => {
		vscode.window.showInformationMessage('卧槽泥马');
	});

	// 新增打开React页面的命令
	const openReactPageCommand = vscode.commands.registerCommand('aicode.openReactPage', () => {
		// 创建并显示webview面板
		const panel = vscode.window.createWebviewPanel(
			'reactPage',
			'AI Code React Page',
			vscode.ViewColumn.One,
			{
				enableScripts: true,
				localResourceRoots: [context.extensionUri]
			}
		);

		// 设置webview内容
		const scriptUri = panel.webview.asWebviewUri(vscode.Uri.joinPath(context.extensionUri, 'dist', 'webview.js'));
		const htmlPath = path.join(context.extensionUri.fsPath, 'src', 'webview', 'index.html');
		let html = fs.readFileSync(htmlPath, 'utf8');
		html = html.replace('{{webviewScript}}', scriptUri.toString());
		panel.webview.html = html;

		// 监听来自webview的消息
		panel.webview.onDidReceiveMessage(
			message => {
				switch (message.command) {
					case 'alert':
						vscode.window.showInformationMessage(message.data);
						return;
				}
			},
			undefined,
			context.subscriptions
		);
	});

	context.subscriptions.push(disposable, openReactPageCommand);
}

// This method is called when your extension is deactivated
export function deactivate() { }
